import { IDate } from '../helpers';
import { ICurrency } from './global';
import { ISurCharge } from './housekeeping';

export type IPrice = {
  baseCost?: number;
  cost?: number;
  finalCost?: number;
  increaseReasons?: {
    key: string;
    value: number;
  }[];
  decreasedReasons?: {
    key: string;
    value: number;
    promotionBy: string;
  }[];
  duration?: number;
  date?: string;
  currency?: ICurrency;
  reason?: string;
  isIncrease?: boolean;
  transportFee?: number;
  depositMoney?: number;
  totalArea?: number;
  promotionBy?: string;
  newFinalCost?: number;
  vat?: number;
  totalCost?: number;
  subTasksCostDetail?: {
    beautyCarePackages?: any[];
    massagePackages?: any[];
    movingCostDetail?: IPrice;
    oldHomeCleaningCostDetail?: IPrice;
    newHomeCleaningCostDetail?: IPrice;
  };
  dateOptions?: string[];
  excludeTaskerFee?: ISurCharge[];
  timezone?: string;
  collectionDate?: string;
  isCanGetDistance?: boolean;
};

export type IPricingSub = {
  date: IDate;
  duration: number;
  costDetail: IPrice;
};

export type IPriceSub = {
  pricing: IPricingSub[];
  session?: number;
  discount?: number;
  error?: string;
  numberIncreasement?: number;
} & IPrice;
