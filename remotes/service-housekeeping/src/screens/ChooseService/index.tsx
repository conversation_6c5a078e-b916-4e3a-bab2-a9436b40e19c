import React, { useMemo } from 'react';
import {
  Alert,
  BlockView,
  ColorsV2,
  CText,
  IconAssets,
  IHostelType,
  KeyboardAware,
  PriceButton,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { HostelType, RenderAddress, RoomType } from '@components';
import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

export const ChooseService = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();

  const {
    service,
    price,
    address,
    homeType,
    rooms,
    options,
    roomNumber,
    setHomeType,
    resetHousekeepingState,
  } = usePostTaskStore();

  const listHostelType = useMemo(() => {
    return (
      service?.detailService?.housekeeping?.city?.find(
        (item) => item.name === address?.city,
      )?.homeTypes || []
    );
  }, [service, address]);

  const handleChange = (type: IHostelType) => {
    resetHousekeepingState();
    setHomeType(type);
  };

  const onChangeHostelType = (type: IHostelType) => {
    // If no home type selected yet
    if (isEmpty(homeType)) {
      setHomeType(type);
      return;
    }

    // If selecting the same type again, do nothing
    if (homeType?.name === type?.name) {
      return;
    }

    // If home type selected but no rooms/options/room number, just change
    if (isEmpty(rooms) && isEmpty(options) && !roomNumber) {
      handleChange(type);
      return;
    }

    // If home type selected and has data, show confirmation dialog
    Alert.alert.open({
      title: t('CHANGE_TYPE_HOME'),
      message: (
        <BlockView>
          <CText margin={{ bottom: Spacing.SPACE_08 }}>
            {t('DESCRIPTION_CHANGE_TYPE_HOME')}
          </CText>
          <CText>{t('CONFIRMED_CHANGE')}</CText>
        </BlockView>
      ),
      actions: [
        {
          text: t('BTN_BACK'),
          style: 'cancel',
        },
        {
          text: t('CONTINUE'),
          onPress: () => handleChange(type),
        },
      ],
    });
  };

  const onConfirmed = async () => {
    navigation.navigate(RouteName.ChooseDateTime);
  };

  if (isEmpty(listHostelType)) {
    return (
      <BlockView style={styles.container}>
        <CText
          center
          margin={20}
        >
          {t('NOT_SUPPORT_CITY')}
        </CText>
      </BlockView>
    );
  }

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <KeyboardAware contentContainerStyle={styles.contentContainerStyle}>
        <RenderAddress
          name={address?.locationName}
          description={address?.address}
          icon={IconAssets.icHotelFill}
          descriptionStyle={{ color: ColorsV2.neutral600 }}
          ComponentRightButton={
            <TouchableOpacity
              onPress={() =>
                navigation.navigate(RouteName.ChooseHostel, {
                  isHideCurrentLocation: true,
                })
              }
            >
              <CText color={ColorsV2.green500}>{t('EDIT')}</CText>
            </TouchableOpacity>
          }
        />
        <HostelType
          onChangeHostelType={onChangeHostelType}
          typeHostelSelected={homeType}
          hostelTypes={listHostelType}
        />
        <RoomType hostelType={homeType} />
      </KeyboardAware>

      <PriceButton
        testID="btnNextStep3"
        onPress={onConfirmed}
        fromScreen={service?.name}
        pricePostTask={price}
      />
    </BlockView>
  );
};
