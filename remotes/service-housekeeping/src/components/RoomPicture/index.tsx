import React from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FastImage,
  FontSizes,
  IHouseKeepingOption,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { imgCamera } from '@images';
import { usePostTaskStore } from '@stores';

import { TakeRoomPicture } from '../TakeRoomPicture';
import { styles } from './styles';

const ListRoomEmpty = () => {
  const { t } = useI18n();
  return (
    <BlockView style={styles.emptyContainer}>
      <BlockView center>
        <FastImage
          source={imgCamera}
          style={styles.imgEmpty}
        />
      </BlockView>
      <SizedBox
        height={1}
        color={ColorsV2.neutral100}
        margin={{ vertical: Spacing.SPACE_16 }}
      />
      <CText
        center
        color={ColorsV2.neutral600}
      >
        {t('PLEASE_CHOOSE_ROOM')}
      </CText>
    </BlockView>
  );
};

interface IRoomPictureProps {
  options?: IHouseKeepingOption[];
}

export const RoomPicture = ({ options }: IRoomPictureProps) => {
  const { t } = useI18n();
  const { rooms } = usePostTaskStore();

  const setUpFollowImage = options?.find(
    (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM',
  );

  return (
    <BlockView margin={{ vertical: Spacing.SPACE_16 }}>
      <CText
        bold
        size={FontSizes.SIZE_18}
      >
        {t('LABEL_SET_PICTURE')}
      </CText>
      <CText margin={{ vertical: Spacing.SPACE_08 }}>
        {t('CONTENT_SET_PICTURE')}
      </CText>
      <ConditionView
        condition={Boolean(setUpFollowImage?.duration)}
        viewTrue={
          <CText color={ColorsV2.green500}>
            {t('MINUTE_PER_ROOM', {
              t: Math.round((setUpFollowImage?.duration || 0) * 60),
            })}
          </CText>
        }
      />
      <ConditionView
        condition={isEmpty(rooms)}
        viewTrue={<ListRoomEmpty />}
        viewFalse={<TakeRoomPicture options={options} />}
      />
    </BlockView>
  );
};
