import React from 'react';
import { TouchableOpacity } from 'react-native';
import {
  BlockView,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  getTextWithLocale,
  IconAssets,
  IconImage,
  IHouseKeepingOption,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { imgCleaningLobby } from '@images';

import { styles } from './styles';

interface IModalContentProps {
  handleClose: () => void;
  option?: IHouseKeepingOption;
}

export const ModalDescriptionCleaningLobby = ({
  option,
  handleClose,
}: IModalContentProps) => {
  const { t } = useI18n();

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <BlockView backgroundColor="red">
        <BlockView
          absolute
          positionRight={Spacing.SPACE_16}
          positionTop={Spacing.SPACE_16}
        >
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
          >
            <IconImage
              source={IconAssets.icRemoveCircle}
              color={ColorsV2.neutral800}
            />
          </TouchableOpacity>
        </BlockView>
        <FastImage
          resizeMode="cover"
          source={imgCleaningLobby}
          style={styles.backgroundImg}
        />
      </BlockView>

      <BlockView style={styles.content}>
        <CText
          bold
          size={FontSizes.SIZE_18}
          margin={{ bottom: Spacing.SPACE_16 }}
        >
          {getTextWithLocale(option?.text)}
        </CText>

        <CText margin={{ bottom: Spacing.SPACE_08 }}>
          {t('DESCRIPTION_CLEANING_LOBBY')}
        </CText>
        <CText color={ColorsV2.orange500}>
          {t('TIME_CLEANING_LOBBY', {
            t: Math.round((option?.duration || 0) * 60),
          })}
        </CText>
      </BlockView>
    </BlockView>
  );
};
