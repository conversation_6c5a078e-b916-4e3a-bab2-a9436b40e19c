import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  DeviceHelper,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_16,
    overflow: 'hidden',
  },
  backgroundImage: {
    height: 200,
    backgroundColor: ColorsV2.neutral100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backgroundImg: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: DeviceHelper.WINDOW.WIDTH / 2,
  },
  content: {
    padding: Spacing.SPACE_16,
    position: 'relative',
  },
  closeButton: {
    zIndex: 1,
  },
});
