import React, { memo, useMemo } from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  CTextInput,
  CustomWarning,
  FontSizes,
  getTextWithLocale,
  IHostelType,
  IRoomType,
  NameHostelType,
  ProcessButton,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { NumberSpinner } from '../NumberSpinner';
import { OptionsHouseKeeping } from '../OptionsHouseKeeping';
// import { ProcessButton } from '../ProcessButton';
import { RoomPicture } from '../RoomPicture';
import { Surcharge } from '../Surcharge';
import { styles } from './styles';

interface IRoomItemProps {
  room?: IRoomType;
  isLast?: boolean;
  currentRoom?: IRoomType;
  onPress: (value: IRoomType) => void;
  callBack?: () => void;
}

const RoomItem = memo(
  ({ room, isLast, currentRoom, onPress, callBack }: IRoomItemProps) => {
    return (
      <BlockView>
        <BlockView
          flex
          row
          horizontal
          jBetween
        >
          <BlockView style={styles.left}>
            <CText bold>{getTextWithLocale(room?.text)}</CText>
            <CText
              color={ColorsV2.neutral600}
              margin={{ top: Spacing.SPACE_04 }}
            >
              {getTextWithLocale(room?.description)}
            </CText>
          </BlockView>
          <BlockView style={styles.right}>
            <NumberSpinner
              testID={room?.name}
              amount={currentRoom?.quantity}
              data={room}
              onPress={onPress}
              callBack={callBack}
            />
          </BlockView>
        </BlockView>
        {!isLast ? (
          <SizedBox
            height={1}
            color={ColorsV2.neutral100}
            margin={{ vertical: Spacing.SPACE_12 }}
          />
        ) : null}
      </BlockView>
    );
  },
);

interface IRoomTypeProps {
  hostelType?: IHostelType | null;
}

export const RoomType = ({ hostelType }: IRoomTypeProps) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { rooms, roomNumber, setRooms, setRoomNumber } = usePostTaskStore();

  const totalQuantity = useMemo(() => {
    return rooms.reduce(
      (sum: number, room: IRoomType) => sum + (room?.quantity || 0),
      0,
    );
  }, [rooms]);

  const headerContent = useMemo(() => {
    if (hostelType?.name === NameHostelType.Apartment) {
      return {
        title: t('CHOOSE_ROOM_TYPE_HOUSE'),
        content: t('CONTENT_CHOOSE_ROOM_TYPE_HOUSE'),
      };
    }
    return {
      title: t('CHOOSE_ROOM_TYPE'),
      content: t('CONTENT_CHOOSE_ROOM_TYPE'),
    };
  }, [hostelType?.name, t]);

  const handleChangeRoom = async (value: IRoomType) => {
    const existingRoomIndex = rooms.findIndex(
      (room) => room.name === value.name,
    );
    const updatedRooms = [...rooms];

    if (existingRoomIndex >= 0) {
      if (value.quantity === 0) {
        // Remove room if quantity is 0
        updatedRooms.splice(existingRoomIndex, 1);
      } else {
        // Update existing room
        updatedRooms[existingRoomIndex] = value;
      }
    } else if (value.quantity && value.quantity > 0) {
      // Add new room
      updatedRooms.push(value);
    }

    setRooms(updatedRooms);
  };

  if (!hostelType) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <CText
        bold
        size={FontSizes.SIZE_18}
      >
        {headerContent.title}
      </CText>
      <CText style={styles.description}>{headerContent.content}</CText>
      <BlockView
        backgroundColor={ColorsV2.neutralWhite}
        style={styles.roomContainer}
      >
        {hostelType?.roomTypes?.map((item, index) => {
          const isLast =
            index ===
            (hostelType?.roomTypes && hostelType.roomTypes.length - 1);
          const currentRoom = rooms?.find(
            (room: IRoomType) => room?.name === item?.name,
          );
          return (
            <RoomItem
              key={`${item?.name}`}
              isLast={isLast}
              room={item}
              currentRoom={currentRoom}
              onPress={handleChangeRoom}
            />
          );
        })}
        <ConditionView
          condition={!isEmpty(rooms)}
          viewTrue={
            <BlockView>
              <SizedBox
                height={1}
                color={ColorsV2.neutral100}
                margin={{
                  top: Spacing.SPACE_12,
                  bottom: Spacing.SPACE_08,
                }}
              />
              <CTextInput
                value={roomNumber}
                placeholder={t('PLACEHOLDER_ROOM_NUMBER')}
                onChangeText={(value) => {
                  setRoomNumber(value);
                }}
                containerStyle={styles.txtInput}
              />
            </BlockView>
          }
        />
      </BlockView>
      <Surcharge />

      <SizedBox height={Spacing.SPACE_16} />

      <ConditionView
        condition={Boolean(hostelType?.name === NameHostelType.Villa)}
        viewTrue={
          <CustomWarning
            content={t('NOTE_FOR_VILLA')}
            margin={{ vertical: Spacing.SPACE_08 }}
          />
        }
      />
      <ProcessButton
        onPress={() => navigation.navigate(RouteName.WorkingProcess)}
      />

      <SizedBox height={Spacing.SPACE_16} />

      <RoomPicture options={hostelType?.options} />
      <OptionsHouseKeeping options={hostelType?.options} />
    </BlockView>
  );
};
