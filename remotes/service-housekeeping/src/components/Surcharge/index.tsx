import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  getTextWithLocale,
  IconImage,
  IObjectText,
  showPriceAndCurrency,
  Spacing,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

export const SurchargeItem = ({
  icon,
  title,
  cost,
}: {
  isShow?: boolean;
  icon?: string;
  title?: IObjectText;
  cost?: number;
}) => {
  if (!cost) {
    return <></>;
  }
  return (
    <BlockView
      row
      padding={Spacing.SPACE_12}
      border={{ width: 1, color: ColorsV2.orange500 }}
      margin={{ vertical: Spacing.SPACE_08 }}
      radius={BorderRadius.RADIUS_08}
      backgroundColor={ColorsV2.orange50}
    >
      <IconImage source={{ uri: icon }} />
      <BlockView
        margin={{ left: Spacing.SPACE_12 }}
        flex
      >
        <CText>{getTextWithLocale(title)}</CText>
        <CText
          color={ColorsV2.green500}
          margin={{ top: Spacing.SPACE_04 }}
        >
          +{showPriceAndCurrency(cost)}
        </CText>
      </BlockView>
    </BlockView>
  );
};

export const Surcharge = () => {
  const { price } = usePostTaskStore();

  if (isEmpty(price?.excludeTaskerFee)) {
    return <></>;
  }

  return price?.excludeTaskerFee?.map((item) => {
    return (
      <SurchargeItem
        cost={item?.price}
        title={item?.text}
        icon={item?.icon}
        key={`surcharge-${item?.name}`}
      />
    );
  });
};
